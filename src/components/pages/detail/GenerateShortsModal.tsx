import { useMemo, useRef, useEffect, useState } from 'react'
import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { screenOrientationAtom } from '@/stores'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { useAtomValue } from 'jotai'
import { MyMirrorAiTask } from '@/stores/types'
import { ShortsStatusEnum } from '@/apis/types'
import { publicPreLoadSourceObj } from '../../../configs/source'
import { VideoPlayer, VideoPlayerRef } from '../result/VideoPlayer'

export const GenerateShortsModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  shorts: {
    id?: string
    origin_url?: string
    url?: string
    status?: ShortsStatusEnum
    job_id?: string
    image_start_url?: string
  } | null
}> = ({ open, setOpen, shorts }) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const videoPlayerRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [showOverlay, setShowOverlay] = useState(true) // 初始显示遮罩层

  const handleClose = () => {
    setOpen?.(false)}

  // 播放视频
  const handlePlay = async () => {
    if (videoPlayerRef.current) {
      try {
        await videoPlayerRef.current.play()
        setIsPlaying(true)
        setShowOverlay(false)
      } catch (error) {
        console.error('视频播放失败:', error)
      }
    }
  }

  // 暂停视频
  const handlePause = () => {
    if (videoPlayerRef.current) {
      videoPlayerRef.current.pause()
      setIsPlaying(false)
      setShowOverlay(true)
    }
  }

  const isPending = useMemo(() => {
    return (
      !shorts ||
      shorts?.status === ShortsStatusEnum.PENDING_QUEUE ||
      !shorts?.origin_url
    )
  }, [shorts])

  // 监听弹窗打开/关闭状态，控制视频播放
  useEffect(() => {
    if (open && shorts?.status === ShortsStatusEnum.SUCCESS) {
      // 弹窗打开且视频生成完成，自动播放视频
      console.log('🎬 尝试自动播放视频...')

      // 增加延迟确保组件完全渲染和视频加载
      const timer = setTimeout(() => {
        handlePlay()
      }, 300) // 增加延迟时间
      return () => clearTimeout(timer)
    } else if (!open) {
      // 弹窗关闭，停止播放视频
      handlePause()
      setShowOverlay(true) // 重置遮罩层状态
    }
  }, [open, shorts?.status, shorts?.origin_url, shorts?.url])

  // 监听视频事件
  useEffect(() => {
    const video = videoPlayerRef.current
    if (!video) return

    const handleVideoPlay = () => {
      setIsPlaying(true)
      setShowOverlay(false)
    }

    const handleVideoPause = () => {
      setIsPlaying(false)
      setShowOverlay(true)
    }

    const handleVideoEnded = () => {
      setIsPlaying(false)
      setShowOverlay(true)
    }

    video.addEventListener('play', handleVideoPlay)
    video.addEventListener('pause', handleVideoPause)
    video.addEventListener('ended', handleVideoEnded)

    return () => {
      video.removeEventListener('play', handleVideoPlay)
      video.removeEventListener('pause', handleVideoPause)
      video.removeEventListener('ended', handleVideoEnded)
    }
  }, [shorts?.origin_url, shorts?.url])

  return (
    <MyModal
      title=""
      open={open}
      width={screenOrientation.isLandScape ? 800 : '88vw'}
      content={
        <div className="min-h-[60dvh] w-full">
          {isPending && (
            <div className="py-32 text-center">
              <img src={publicPreLoadSourceObj.painting} alt="" />
              <p className="text-[2rem] text-white/60">{t('生成中')}</p>
            </div>
          )}
          {shorts?.status === ShortsStatusEnum.SUCCESS && (
            <div className="w-full h-full relative">
              <video
                ref={videoPlayerRef}
                src={shorts?.origin_url || shorts?.url}
                poster={shorts?.image_start_url}
                controls={false}
                playsInline
                muted
                preload="metadata"
                className={`w-full h-full rounded-2xl`}
                onClick={isPlaying ? handlePause : handlePlay}
              />

              {/* 遮罩层和播放按钮 */}
              {showOverlay && (
                <div
                  className="absolute inset-0 bg-black/30 rounded-2xl flex items-center justify-center cursor-pointer"
                  onClick={handlePlay}
                >
                  <div className="w-[6rem] h-[6rem]">
                    <img
                      src={publicPreLoadSourceObj.play}
                      className="w-full h-full"
                      alt="播放"
                    />
                  </div>
                </div>
              )}
            </div>
          )}
          {shorts?.status === ShortsStatusEnum.FAILED && (
            <div className="py-32 text-center">
              <p className="text-[2rem] text-white/60">{t('生成失败')}</p>
            </div>
          )}
        </div>
      }
      showOkButton={false}
      contentClassName="p-0 w-full"
      onCancel={handleClose}
    />
  )
}
