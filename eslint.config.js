// eslint.config.js
import js from '@eslint/js'
import tseslint from 'typescript-eslint'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import prettier from 'eslint-plugin-prettier'

export default [
  {
    ignores: ['dist', '.eslintrc.cjs'], // 原 ignorePatterns
  },
  js.configs.recommended, // eslint:recommended
  ...tseslint.configs.recommended, // plugin:@typescript-eslint/recommended
  reactHooks.configs.recommended, // plugin:react-hooks/recommended
  {
    plugins: {
      'react-refresh': reactRefresh,
      prettier: prettier,
    },
    languageOptions: {
      parser: tseslint.parser,
      ecmaVersion: 2020,
      sourceType: 'module',
      globals: {
        window: 'readonly',
        document: 'readonly',
      },
    },
    rules: {
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      'react-hooks/exhaustive-deps': 0,
      '@typescript-eslint/no-explicit-any': 0,
      'no-async-promise-executor': 0,
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'warn',
      'no-extra-semi': 0,
      'prettier/prettier': 'error',
    },
  },
]
